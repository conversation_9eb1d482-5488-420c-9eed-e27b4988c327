// const { fetch: refreshUserSession } = useUserSession()
const match = import.meta.env.DEV ? 'http://localhost/*' : `${import.meta.env.VITE_WEBSITE}/*`

export default defineContentScript({
  matches: [match],
  main() {
    window.addEventListener('message', (event) => {
      // console.log('ContentScript 收到来自网站的事件', event)
      if (event.data.type === 'website_to_ext') {
        if (event.data.action === 'userChanged') {
          // popup 每次启动都是新实例
          // background 请求自动就会带上 cookie
          // authClient.getSession({
          //   query: { disableCookieCache: true },
          //   fetchOptions: {
          //     cache: 'no-cache' // 确保不使用缓存
          //   }
          // })

          // refreshUserSession();
        }
      }
    });
  },
});
