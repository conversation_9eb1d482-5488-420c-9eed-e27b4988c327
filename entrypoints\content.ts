// const { fetch: refreshUserSession } = useUserSession()
const match = import.meta.env.DEV ? 'http://localhost/*' : `${import.meta.env.VITE_WEBSITE}/*`

export default defineContentScript({
  matches: [match],
  main() {
    // window.addEventListener('message', (event) => {
    //   console.log('ContentScript 收到来自网站的事件', event)
    //   if (event.data.type === 'website_to_ext') {
    //     if (event.data.action === 'userChanged') {
    //       // popup 每次启动都是新实例
    //       // background 请求自动就会带上 cookie
    //       // authClient.getSession({
    //       //   query: { disableCookieCache: true },
    //       //   fetchOptions: {
    //       //     cache: 'no-cache' // 确保不使用缓存
    //       //   }
    //       // })

    //       // refreshUserSession();
    //     }
    //   }
    // });

    // 添加Chrome扩展消息监听器（可选）
    // "Unchecked runtime.lastError: The page keeping the extension port is moved into back/forward cache, so the message channel is closed." 
    // 打开的 web页面 a 跳转到页面b时，页面a会进入 back/forward cache，但 background 仍然在尝试向已经缓存的页面发送消息，导致消息通道关闭。
    // 这个错误不会影响功能，但会在控制台产生噪音。
    // chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    //   if (message.type === 'bookmark-change' || message.action === 'liveQueryUpdate') {
    //     // 可以选择处理或忽略这些消息
    //     console.debug('Received extension message:', message);
    //   }
    //   return false; // 不需要异步响应
    // });
  },
});
