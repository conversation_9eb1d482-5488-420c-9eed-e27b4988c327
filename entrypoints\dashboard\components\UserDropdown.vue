<script setup lang="ts">
import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuItem, DropdownMenuGroup } from '@/components/ui/dropdown-menu'
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar'
import Icon from '@/components/Icon.vue'

const { user: currentUser, clear: clearUserSession, fetch: refreshUserSession } = useUserSession()
const { currentProfileUser } = useProfileUser()
const website = import.meta.env.VITE_WEBSITE

import { authClient } from "@/utils/auth-client"
const session = authClient.useSession();
</script>

<template>
  <DropdownMenu v-if="currentUser">
    <DropdownMenuTrigger as-child>
      <Button
        variant="ghost"
        class="flex h-11 w-full items-center gap-2 px-1 data-[state=open]:bg-accent data-[state=open]:text-accent-foreground"
      >
        <Avatar class="size-8 rounded-lg">
          <AvatarImage
            :src="currentUser.image || ''"
            :alt="currentUser.name"
          />
          <AvatarFallback class="rounded-lg">
            {{ currentUser.name || currentUser.email }}
          </AvatarFallback>
        </Avatar>
        <div class="grid flex-1 text-left text-sm leading-tight">
          <span class="truncate font-semibold">{{ currentUser.name }}</span>
          <span class="truncate text-xs text-muted-foreground">{{ currentUser.email }}</span>
        </div>
        <Icon name="radix-icons:caret-sort" />
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent
      class="w-[--radix-dropdown-menu-trigger-width] min-w-56"
      side="bottom"
      align="center"
      :side-offset="4"
    >
      <DropdownMenuLabel class="p-0 font-normal">
        <div class="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
          <Avatar class="size-8 rounded-lg">
            <AvatarImage
              :src="currentUser.image || ''"
              :alt="currentUser.name"
            />
            <AvatarFallback class="rounded-lg">
              {{ currentUser.name || currentUser.email }}
            </AvatarFallback>
          </Avatar>
          <div class="grid flex-1 text-left text-sm leading-tight">
            <span class="truncate font-semibold">{{ currentUser.name }}</span>
            <span class="truncate text-xs">{{ currentUser.email }}</span>
          </div>
        </div>
      </DropdownMenuLabel>
      <DropdownMenuSeparator />
      <DropdownMenuItem as-child>
        <RouterLink to="/">
          <Icon
            name="lucide:slash"
            class="mr-2"
          /> Home
        </RouterLink>
      </DropdownMenuItem>
      <DropdownMenuSeparator />
      <DropdownMenuGroup>
        <DropdownMenuItem as-child>
          <RouterLink to="/pricing">
            <span class="mr-2 size-4">✨</span> Upgrade to Pro
          </RouterLink>
        </DropdownMenuItem>
      </DropdownMenuGroup>
      <DropdownMenuSeparator />
      <DropdownMenuGroup>
        <DropdownMenuItem as-child>
          <RouterLink to="/dashboard/settings">
            Account
          </RouterLink>
        </DropdownMenuItem>
        <DropdownMenuItem as-child>
          <RouterLink to="/dashboard/settings/billing">
            Billing
          </RouterLink>
        </DropdownMenuItem>
      </DropdownMenuGroup>
      <DropdownMenuSeparator />
      <DropdownMenuItem @click="clearUserSession">
        <Icon
          name="lucide:log-out"
          class="mr-2"
        />
        Log out
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
  <div v-else>
    <Button
        as-child
        variant="ghost"
        class="rounded-full"
        size="lg"
      >
        <a
          class="font-semibold text-muted-foreground hover:text-foreground"
          :href="`${website}/auth/login`"
          target="_blank"
        >
          {{ i18n.t('options.header.signIn') }}
        </a>
      </Button>
  </div>
</template>
