<script lang="ts" setup>
import { z } from 'zod'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { nextTick } from 'vue'

import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form'
import { Textarea } from '@/components/ui/textarea'
import FolderTree from './components/FolderTree.vue'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'

import Icon from '@/components/Icon.vue';
import type { RxDatabase } from 'rxdb'
import cuid from 'cuid';
import { toast } from 'vue-sonner'
import { faviconURL } from '@/utils'
import { rxDBInstance as rxdb } from '@/rxdb/index'
import EmojiSelector from '@/components/EmojiSelector.vue'
import EditNoteDialog from './components/EditNoteDialog.vue'
import EditBookmarkDialog from './components/EditBookmarkDialog.vue'
import NewFolderDialog from './components/NewFolderDialog.vue'
import UnlockEncryptDialog from '@/components/crypto/UnlockEncryptDialog.vue'
import NeedToLoginDialog from '@/components/NeedToLogin.vue'
import { openOrActivateManage } from '@/utils/bookmark'

const { user: currentUser } = useUserSession()
const { encryptBookmark, decryptBookmark, isLocked, hasStoredEncryption } = useCrypto()

// 对话框状态
const showNeedToLoginDialog = ref(false)
const showUnlockDialog = ref(false)
const isCheckingEncryption = ref(false)

const db = inject<RxDatabase>('db')!

const props = defineProps<{
  id: string,
  browserTab: chrome.tabs.Tab | null,
  existBookmark: chrome.bookmarks.BookmarkTreeNode | null
}>()

const showNewFolderDialog = ref(false)
const folderTreeRef = ref()

// init Bookmark
const editedBookmark = ref({
  id: props.existBookmark?.id || null,
  title: props.existBookmark?.title || props.browserTab?.title || '',
  url: props.existBookmark?.url || props.browserTab?.url || '',
  parentId: props.existBookmark?.parentId || '2'
});

// init Extra
const existExtra = ref<{ note: string; emoji: string; bookmarkType?: string; iv?: string } | null>(null)
const editedExtra = ref({
  note: "",
  emoji: "" as string
})
const existEmoji = ref<string>("")

// init Encryption
const isEncrypted = ref(false)

// 当前加密使用的 IV
const currentEncryptionIV = ref('')
// 存储原始值（用于加密/解密切换）
const originalTitle = ref('')
const originalUrl = ref('')

// 是否可以编辑书签（如果勾选了加密且已解锁，则不允许编辑）
const canEditBookmark = computed(() => {
  return !(isEncrypted.value && !isLocked.value);
});

// 更新加密状态，直接修改 editedBookmark 的值
const updateEncryptedDisplay = async () => {
  if (isEncrypted.value && !isLocked.value) {
    // 切换到加密状态：保存原始值，然后加密
    if (!originalTitle.value) {
      // 首次加密，保存原始值
      originalTitle.value = editedBookmark.value.title;
      originalUrl.value = editedBookmark.value.url;
    }

    // 加密 title
    const encryptedTitle = await encryptBookmark({ title: originalTitle.value });
    if (encryptedTitle) {
      editedBookmark.value.title = encryptedTitle.title;
      currentEncryptionIV.value = encryptedTitle.iv;
    }

    // 加密 url
    if (originalUrl.value) {
      const encryptedUrl = await encryptBookmark({ url: originalUrl.value });
      if (encryptedUrl) {
        editedBookmark.value.url = encryptedUrl.url;
      }
    }
  } else {
    // 切换到未加密状态：恢复原始值
    if (originalTitle.value) {
      editedBookmark.value.title = originalTitle.value;
      editedBookmark.value.url = originalUrl.value;
      // 清空原始值存储
      originalTitle.value = '';
      originalUrl.value = '';
    }
    currentEncryptionIV.value = '';
  }
};

// 处理加密状态切换
const handleEncryptionToggle = async () => {
  const checked = !isEncrypted.value;
  // 如果正在检查中，忽略变化
  if (isCheckingEncryption.value) {
    return;
  }

  if (!checked) {
    // 如果是现有的加密书签，警告用户取消加密会导致明文存储
    if (props.existBookmark && existExtra.value?.bookmarkType === 'encrypted') {
      const confirmed = confirm('This bookmark is currently encrypted. Unchecking will store it as plain text. Are you sure?');
      if (!confirmed) {
        // 用户取消，保持原状态
        return;
      }
    }
    // 用户确认取消加密，设置为未加密状态
    isEncrypted.value = false;
    await updateEncryptedDisplay();
    return;
  }

  // 开始检查加密条件
  isCheckingEncryption.value = true;

  try {
    // 检查是否登录
    if (!currentUser.value) {
      // 检查失败，恢复原状态
      isEncrypted.value = false;
      showNeedToLoginDialog.value = true;
      return;
    }

    // 检查是否已设置密码
    const hasStored = await hasStoredEncryption();

    if (!hasStored && !currentUser.value.encryptKey) {
      // 检查失败，恢复原状态
      isEncrypted.value = false;
      const dashboardUrl = browser.runtime.getURL('/dashboard.html#/encrypted');
      await browser.tabs.create({ url: dashboardUrl });
      window.close();
      return;
    }

    // 检查是否已解锁
    if (isLocked.value) {
      // 检查失败，恢复原状态
      isEncrypted.value = false;
      showUnlockDialog.value = true;
      return;
    }

    // 所有检查通过，设置加密状态
    isEncrypted.value = true;
    // 更新加密显示内容
    await updateEncryptedDisplay();
  } finally {
    // 无论成功还是失败，都重置加载状态
    isCheckingEncryption.value = false;
  }
};
async function resetExtra() {
  if (props.existBookmark) {
    console.warn('--------- existBookmark ---------', props.existBookmark)
    existExtra.value = await db.extras.findOne({
      selector: {
        bookmarkId: props.existBookmark.id
      }
    }).exec()

    if (existExtra.value) {
      editedExtra.value = {
        note: existExtra.value.note,
        emoji: existExtra.value.emoji
      }
      existEmoji.value = existExtra.value.emoji

      // 检查是否为加密书签
      isEncrypted.value = existExtra.value.bookmarkType === 'encrypted'

      // 如果是加密书签，需要处理显示逻辑
      if (isEncrypted.value) {
        if (!isLocked.value) {
          // 已解锁：解密当前的 title 和 url 来获取原始值
          try {
            const decryptedData = await decryptBookmark({
              title: editedBookmark.value.title,
              url: editedBookmark.value.url,
              iv: existExtra.value.iv
            });

            if (decryptedData) {
              originalTitle.value = decryptedData.title;
              originalUrl.value = decryptedData.url || '';
              currentEncryptionIV.value = existExtra.value.iv || '';
            }
          } catch (error) {
            console.warn('Failed to decrypt existing bookmark:', error);
          }
        }
        // 如果未解锁，editedBookmark 中的值就是加密后的密文，直接显示
      }
    }
  }
}

// init Tags
const allTags = ref<string[]>([])
const existTags = ref<string[]>([])
const selectedTags = ref<string[]>([])
async function resetTags() {
  // init existTags
  const dbTags = await db.tags.find().exec();
  console.warn('--------- resetTags ---------', dbTags)
  allTags.value = dbTags.map((doc: any) => doc.name).sort((a, b) => a.localeCompare(b));

  if (props.existBookmark) {
    // set existTags
    const bookmarkTags = await db.tags.find({
      selector: {
        bookmarkIds: {
          $in: [props.existBookmark.id]
        },
      }
    }).exec()
    existTags.value = bookmarkTags.map(doc => doc.name);
    selectedTags.value = [...existTags.value];
  }
}

provide('setSelectedFolderId', (folderId: string) => {
  console.warn('--------- setSelectedFolderId ---------', folderId)
  editedBookmark.value.parentId = folderId;
});

const emit = defineEmits(['remove', 'updateExistBookmark']);

const removeBookmark = async () => {
  if (!editedBookmark.value.id) return;
  try {
    await browser.bookmarks.remove(editedBookmark.value.id);
    emit('remove', props.id); // 触发 remove 事件，传递当前 tab 的 id
    window.close();
    toast.success(i18n.t('popup.edit.removed'))
  } catch (error) {
    console.error('删除书签失败:', error);
    toast.error(i18n.t('error.unknown'))
  }
};

// 通过获取最近更新的书签，从而获取最近更新的文件夹，去重后保留最近的 5 个，再添加上默认文件夹
const recentFolders = ref<any[]>([]);
async function resetRecentFolders() {
  // 清空现有的 recentFolders 数组，避免重复
  recentFolders.value = [];

  // 获取最近创建的书签，按创建时间排序（修改不算），最新的在最前面
  const recentbookmarks = await browser.bookmarks.getRecent(30)
  console.warn('--------- recentbookmarks ---------', recentbookmarks)

  // 从最近的书签中提取父文件夹ID并去重
  const parentIds = [...new Set(recentbookmarks.map(node => node.parentId).filter(Boolean))]

  // 去掉默认文件夹，因为默认文件夹应该始终在列表最后
  const defaultFolderIds = ['1', '2', '3']
  const filteredParentIds = parentIds.filter(id => id && !defaultFolderIds.includes(id)) as string[]
  const uniqueParentIds = [...filteredParentIds.slice(0, 5), '1', '2', '3']

  // 遍历 uniqueParentIds 获取文件夹列表
  for (const id of uniqueParentIds) {
    try {
      const folders = await browser.bookmarks.get([id as string]);
      if (folders && folders.length > 0) {
        recentFolders.value.push(folders[0]);
      }
    } catch (error) {
      console.log(`Folder ${id} not found`);
    }
  }
}

// 设置书签的父文件夹ID并清除待选择状态
// 优先级：当前创建的新文件夹 - 已存在的书签的父文件夹 - 列表的第一个文件夹 - 默认文件夹 ‘2’
async function setBookmarkParentId() {
  if (pendingFolderSelection.value) {
    editedBookmark.value.parentId = pendingFolderSelection.value;
    // 清除待选择状态
    pendingFolderSelection.value = null;
    // 等待 DOM 更新后滚动到当前文件夹
    await nextTick();
    folderTreeRef.value?.scrollToCurrentFolder();
  } else {
    const latestFolderId = recentFolders.value[0]?.id || '2';
    editedBookmark.value.parentId = props.existBookmark?.parentId || latestFolderId;
  }
}

const saving = ref(false);
const onSubmit = async () => {
  saving.value = true;
  console.warn('---------- editedBookmark ---------', editedBookmark.value)
  console.warn('---------- editedExtra ---------', editedExtra.value)
  console.warn('---------- selectedTags ---------', selectedTags.value)
  console.warn('---------- currentUser ---------', currentUser.value)
  console.warn('---------- isEncrypted ---------', isEncrypted.value)

  const browserAccount = await browser.identity.getProfileUserInfo()
  console.log('getProfileUserInfo --- ', browserAccount)

  try {
    let targetBookmark = null
    // 直接使用 editedBookmark 的值（已经是加密或未加密的最终值）
    let bookmarkData = {
      title: editedBookmark.value.title,
      url: editedBookmark.value.url
    }

    if (props.existBookmark) {
      // 修改 bookmark
      targetBookmark = await browser.bookmarks.update(props.existBookmark.id, bookmarkData)
      // 移动 bookmark
      if (props.existBookmark.parentId !== editedBookmark.value.parentId) {
        await browser.bookmarks.move(props.existBookmark.id, {
          parentId: editedBookmark.value.parentId,
        })
      }
      editedBookmark.value.id = targetBookmark.id
    } else {
      targetBookmark = await browser.bookmarks.create({
        ...bookmarkData,
        parentId: editedBookmark.value.parentId
      })
      editedBookmark.value.id = targetBookmark.id
      emit('updateExistBookmark', targetBookmark)
    }
    console.warn('---------- targetBookmark ---------', targetBookmark)
    // 处理 extra 数据，如果已有 extra 那么直接更新，如果没有，检查是否有数据，有数据则创建 extra
    const query = db.extras.findOne({
      selector: {
        bookmarkId: targetBookmark.id,
        browserAccountId: browserAccount.id,
        userId: currentUser.value?.id
      }
    })
    const dbExtra = await query.exec()

    // 准备 extra 数据，包括加密相关字段
    const extraData = {
      note: editedExtra.value.note?.trim() || '',
      emoji: editedExtra.value.emoji,
      ...(isEncrypted.value && {
        bookmarkType: 'encrypted',
        iv: currentEncryptionIV.value
      }),
      updatedAt: new Date().toISOString(),
    }

    if (dbExtra) {
      existExtra.value = await dbExtra.patch(extraData);
    } else {
      // 如果是加密书签或者有 note/emoji 数据，则创建 extra 记录
      if (isEncrypted.value || editedExtra.value.note?.trim() !== "" || editedExtra.value.emoji) {
        existExtra.value = await db.extras.insert({
          id: cuid(),
          bookmarkId: targetBookmark.id,
          browserAccountId: browserAccount.id,
          userId: currentUser.value?.id,
          ...extraData,
          createdAt: new Date().toISOString(),
        })
      }
    }
    // 处理 tags 数据
    await Promise.all(selectedTags.value.map(async (tag) => {
      const existTag = await db.tags.findOne({
        selector: {
          browserAccountId: browserAccount.id,
          userId: currentUser.value?.id,
          name: tag
        }
      }).exec()
      if (existTag) {
        await existTag.patch({
          bookmarkIds: Array.from(new Set([...existTag.bookmarkIds, targetBookmark.id])), // set the age of every found to 12
          updatedAt: new Date().toISOString(),
        });
      } else {
        await db.tags.insert({
          id: cuid(),
          userId: currentUser.value?.id,
          browserAccountId: browserAccount.id,
          name: tag,
          bookmarkIds: [targetBookmark.id],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        })
      }
    }))
    // initTab()
    window.close();
  } catch (error) {
    console.warn('Error: ', error)
    toast.error((error as any)?.data?.statusMessage || i18n.t('error.unknown'))
  } finally {
    saving.value = false;    
  }
}

// 打开书签管理器
function handleOpenManage() {
  if (props.existBookmark) {
    openOrActivateManage(props.existBookmark.parentId, props.existBookmark.id);
  } else {
    openOrActivateManage();
  }
}

// 添加对话框控制状态
const isEditNoteDialogOpen = ref(false)
const isEditBookmarkDialogOpen = ref(false)

function handleDialogUpdate(data: any) {
  // 更新 editedExtra
  editedExtra.value = data.extra;
  // 更新 selectedTags
  selectedTags.value = data.tags;
}

function handleBookmarkUpdate(data: any) {
  // 更新 editedBookmark
  editedBookmark.value.title = data.bookmark.title;
  editedBookmark.value.url = data.bookmark.url;
}

// 处理新文件夹创建后的逻辑
const pendingFolderSelection = ref<string | null>(null);

async function handleNewFolderSaved(newFolder: any) {
  if (newFolder) {
    // 记录待选择的文件夹ID
    pendingFolderSelection.value = newFolder.id;
  }
}

async function handleRuntimeMessage(request: any) {
  console.warn('--------- popup Edit 收到消息 -----------!', request);
  if (request.type === 'bookmark-change') {
    await resetRecentFolders();
    await setBookmarkParentId();
  }
}

onMounted(async () => {
  // 只在首次加载时初始化
  resetExtra()
  resetTags()
  await resetRecentFolders()
  setBookmarkParentId()
  // input.value = editedBookmark.value.title
  browser.runtime.onMessage.addListener(handleRuntimeMessage);

  // useCrypto() 会自动检查和恢复加密状态
})

onBeforeUnmount(() => {
  browser.runtime.onMessage.removeListener(handleRuntimeMessage);
  console.warn('---------- edit onBeforeUnmount')
});
</script>

<template>
  <form
    :id="`editForm-${props.id}`"
    class="mt-0 flex flex-col"
    @submit.prevent="onSubmit"
  >
    <div class="flex flex-col gap-4 p-4">
      <!-- 书签信息和操作按钮 -->
      <div class="flex items-center justify-between gap-2">
        <!-- 左侧：图标和书签信息 -->
        <div class="flex items-center gap-2 flex-1 min-w-0 mr-4">
          <div class="flex items-center justify-center border w-12 h-12 rounded flex-shrink-0">
            <img class="h-4 w-4 mt-[2px]" :src="faviconURL(editedBookmark.url)" alt="">
          </div>
          <div class="flex flex-col gap-1 min-w-0">
            <span
              class="text-sm font-medium truncate inline-block max-w-fit"
              :class="{ 'cursor-pointer': canEditBookmark, 'cursor-not-allowed opacity-60': !canEditBookmark }"
              @click="canEditBookmark && (isEditBookmarkDialogOpen = true)"
            >
              {{ editedBookmark.title }}
            </span>
            <p
              class="text-sm text-muted-foreground truncate inline-block max-w-fit"
              :class="{ 'cursor-pointer': canEditBookmark, 'cursor-not-allowed opacity-60': !canEditBookmark }"
              @click="canEditBookmark && (isEditBookmarkDialogOpen = true)"
            >
              {{ editedBookmark.url }}
            </p>
          </div>
        </div>

        <!-- 右侧：操作按钮 -->
        <div class="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            class="h-8"
            @click.stop.prevent="isEditNoteDialogOpen = true"
            type="button"
          >
            <Icon name="radix-icons:pencil" class="mr-1" />
            <span>{{ i18n.t('popup.edit.editNote') }}</span>
          </Button>
          <EmojiSelector
            :existEmoji="existEmoji"
            @emojiChange="editedExtra.emoji = $event"
          />
        </div>
      </div>

      <!-- 加密选项 -->
      <div class="flex items-center justify-between">
        <span class="text-sm font-medium text-gray-700">Encryption</span>
        <Button
          variant="outline"
          size="sm"
          type="button"
          :disabled="isCheckingEncryption"
          @click.prevent="handleEncryptionToggle"
          class="h-8 px-3"
        >
          <!-- 加载状态 -->
          <Icon v-if="isCheckingEncryption" name="lucide:loader-2" class="h-4 w-4 animate-spin mr-1" />
          <!-- 加密状态图标 -->
          <Icon v-else-if="isEncrypted" name="lucide:shield-check" class="h-4 w-4 mr-1 text-green-600" />
          <!-- 未加密状态图标 -->
          <Icon v-else name="lucide:shield-off" class="h-4 w-4 mr-1 text-gray-400" />
          <!-- 锁定状态图标 -->
          <Icon v-if="!isCheckingEncryption && isLocked" name="lucide:lock" class="h-4 w-4 ml-1 text-amber-500" />

          <span class="text-xs">
            {{ isCheckingEncryption ? 'Checking...' : isEncrypted ? 'Encrypted' : 'Not Encrypted' }}
          </span>
        </Button>
      </div>

      <!-- 文件夹选择器 -->
      <FolderTree
        ref="folderTreeRef"
        :current-folder-id="editedBookmark.parentId"
        :recent-folders="recentFolders"
        @new-folder="showNewFolderDialog = true"
      />
    </div>
  
    <div class="flex justify-between gap-2 bg-secondary p-4 pl-0">
      <div class="flex">
        <Button
          type="button"
          variant="ghost"
          @click="handleOpenManage"
        >
          <Icon name="radix-icons:home" />
        </Button>
        <Button
          @click.stop.prevent="showNewFolderDialog = !showNewFolderDialog"
          variant="outline"
        >
          {{ i18n.t('popup.edit.newFolder') }}
        </Button>
      </div>
      <div class="flex gap-2">
        <Button
          @click="removeBookmark"
          type="button"
          variant="ghost"
          :disabled="!editedBookmark.id"
          class=" text-muted-foreground"
        >
          <Icon name="radix-icons:cross-2" /> {{ i18n.t('popup.edit.remove') }}
        </Button>
        <Button
          type="submit"
          :form="`editForm-${props.id}`"
          :disabled="saving"
        >
          {{ editedBookmark.id ? i18n.t('popup.edit.update') : i18n.t('popup.edit.add') }}
        </Button>
      </div>
    </div>
    
    <!-- 添加编辑备注对话框 -->
    <EditNoteDialog
      :open="isEditNoteDialogOpen"
      :selectedTags="selectedTags"
      :all-tags="allTags"
      :editedExtra="editedExtra"
      @open-change="(val: boolean) => isEditNoteDialogOpen = val"
      @update="handleDialogUpdate"
    />

    <!-- 添加编辑书签对话框 -->
    <EditBookmarkDialog
      :open="isEditBookmarkDialogOpen && canEditBookmark"
      :editedBookmark="editedBookmark"
      @open-change="(val: boolean) => isEditBookmarkDialogOpen = val"
      @update="handleBookmarkUpdate"
    />

    <NewFolderDialog
      :open="showNewFolderDialog"
      :current-folder-id="editedBookmark.parentId"
      @open-change="(open: boolean) => (showNewFolderDialog = open)"
      @saved="handleNewFolderSaved"
    />

    <!-- 登录提示对话框 -->
    <NeedToLoginDialog
      :open="showNeedToLoginDialog"
      @openChange="(value: boolean) => showNeedToLoginDialog = value"
    />

    <!-- 解锁加密对话框 -->
    <UnlockEncryptDialog
      :open="showUnlockDialog"
      @openChange="(value: boolean) => showUnlockDialog = value"
      @unlocked="async () => { showUnlockDialog = false; isEncrypted = true; await updateEncryptedDisplay(); }"
    />
  </form>
</template>