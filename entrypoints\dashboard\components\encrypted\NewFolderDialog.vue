<script setup lang="ts">
import { z } from 'zod'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { useCrypto } from '@/composables/useCrypto'
import { toast } from 'vue-sonner'
import { Dialog, DialogTitle, DialogContent, DialogHeader, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { rxDBInstance as rxdb } from '@/rxdb/index'

const { user: currentUser } = useUserSession()

const props = defineProps<{
  open: boolean
}>()

const emit = defineEmits<{
  openChange: [open: boolean]
  saved: [bookmark: object | null]
}>()

const bookmarkSchema = toTypedSchema(
  z.object({
    title: z.string().min(2),
    url: z.string().url().optional(),
  }),
)
const form = useForm({
  validationSchema: bookmarkSchema,
})

const { createEncryptedBookmark } = useCrypto()

const onSubmit = form.handleSubmit(async (values) => {
  try {
    // 创建加密书签，使用默认的父文件夹 ID '2'（其他书签）
    const chromeBookmark = await createEncryptedBookmark(values, '2')

    if (!chromeBookmark) {
      return // 如果创建失败，中止提交
    }

    emit('saved', chromeBookmark)
    emit('openChange', false)
  }
  catch (error) {
    console.warn('Error: ', error)
    toast.error(i18n.t('error.unknown'))
  }
})
</script>

<template>
  <Dialog
    :open="open"
    @update:open="(value: boolean) => emit('openChange', value)"
  >
    <DialogContent class="sm:max-w-[425px]">
      <DialogHeader>
        <DialogTitle>New Encrypted Bookmark</DialogTitle>
        <DialogDescription>
          Create a new encrypted bookmark. Your data will be encrypted and stored securely.
        </DialogDescription>
      </DialogHeader>

      <form
        id="dialogForm"
        class="flex flex-col items-stretch gap-4"
        :validation-schema="bookmarkSchema"
        @submit="onSubmit"
      >
        <FormField
          v-slot="{ componentField }"
          name="title"
        >
          <FormItem>
            <FormControl>
              <Input
                type="text"
                placeholder="Bookmark title"
                v-bind="componentField"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>

        <FormField
          v-slot="{ componentField }"
          name="url"
        >
          <FormItem>
            <FormControl>
              <Input
                type="url"
                placeholder="https://example.com (optional)"
                v-bind="componentField"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>
      </form>

      <DialogFooter>
        <Button
          type="submit"
          form="dialogForm"
        >
          Create
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
