import {
  generateEncrypt<PERSON><PERSON>,
  resetEncrypt<PERSON><PERSON>,
  restoreDataKey,
  encryptGCM,
  decryptGCM,
  generateIv,
  arrayBufferToBase64,
  base64ToArrayBuffer
} from '@/utils/crypto'
import { toast } from 'vue-sonner'
import { rxDBInstance as rxdb } from '@/rxdb/index'

// 业务常量
const STORAGE_KEY = 'encryption_info';
const FREE_USER_BOOKMARK_LIMIT = 5;
const LOCK_TIMEOUT = 5 * 60 * 1000; // 5分钟

// 状态
const userDataKey = ref<CryptoKey | null>(null);
const masterIV = ref<Uint8Array | null>(null);
const isLocked = computed(() => userDataKey.value === null);

// 移除定时器，改为与 background script 通信

// 全局初始化标志，确保只初始化一次
let isInitialized = false;

export function useCrypto() {
  // 监听存储变化，同步加密状态
  const handleStorageChange = (changes: { [key: string]: chrome.storage.StorageChange }, areaName: string) => {
    if (areaName === 'local' && changes[STORAGE_KEY]) {
      const newValue = changes[STORAGE_KEY].newValue;
      const oldValue = changes[STORAGE_KEY].oldValue;

      console.log('Storage change detected:', {
        newValue: !!newValue,
        oldValue: !!oldValue,
        isLocked: isLocked.value
      });

      // 如果存储数据被清除，锁定当前状态
      if (!newValue && oldValue) {
        if (!isLocked.value) {
          console.log('Auto-lock detected from storage change');
          userDataKey.value = null;
          masterIV.value = null;
          toast.info("Encryption auto-locked due to inactivity");
        }
      }

      // 如果存储数据被创建或更新，尝试恢复状态
      if (newValue) {
        if (isLocked.value) {
          console.log('Auto-unlock detected from storage change');
          checkAndRestoreFromStorage();
        }
      }
    }
  };

  // 检查并从存储恢复加密状态
  const checkAndRestoreFromStorage = async () => {
    try {
      const stored = await getStoredEncryptionData();
      if (!stored) {
        console.log('checkAndRestoreFromStorage: no stored data');
        return;
      }

      const isValid = (Date.now() - stored.lastActivity <= LOCK_TIMEOUT);
      console.log('checkAndRestoreFromStorage:', {
        stored: !!stored,
        isValid,
        currentlyLocked: isLocked.value,
        timeDiff: Date.now() - stored.lastActivity,
        lockTimeout: LOCK_TIMEOUT
      });

      if (isValid) {
        // 如果当前已锁定，从存储恢复加密状态
        if (isLocked.value) {
          const keyBuffer = base64ToArrayBuffer(stored.userDataKey);
          userDataKey.value = await crypto.subtle.importKey(
            "raw", keyBuffer,
            { name: "AES-GCM", length: 128 },
            true, ["encrypt", "decrypt"]
          );
          masterIV.value = new Uint8Array(base64ToArrayBuffer(stored.masterIV));
          console.log('Auto-restored encryption state from storage');
        } else {
          console.log('Already unlocked, no need to restore');
        }
      } else {
        console.log('Stored data expired, clearing it');
        await clearStoredEncryptionData();
      }
    } catch (error) {
      console.warn('Failed to restore from storage:', error);
    }
  };

  // 设置存储监听（只设置一次）
  if (typeof chrome !== 'undefined' && chrome.storage && !isInitialized) {
    console.log('useCrypto: Setting up storage listener and initial check');
    isInitialized = true;
    chrome.storage.onChanged.addListener(handleStorageChange);

    // 初始化时检查是否有有效的存储状态
    setTimeout(() => {
      console.log('useCrypto: Running initial storage check');
      checkAndRestoreFromStorage();
    }, 100); // 延迟一点确保所有初始化完成
  }



  // ========== Extension Storage 管理 ==========
  const storeEncryptionData = async (userDataKey: CryptoKey, masterIV: Uint8Array) => {
    const data = {
      userDataKey: arrayBufferToBase64(await crypto.subtle.exportKey("raw", userDataKey)),
      masterIV: arrayBufferToBase64(masterIV.buffer as ArrayBuffer),
      lastActivity: Date.now()
    };

    await chrome.storage.local.set({ [STORAGE_KEY]: data });
  };

  const getStoredEncryptionData = async () => {
    const result = await chrome.storage.local.get(STORAGE_KEY);
    return result[STORAGE_KEY] || null;
  };

  const clearStoredEncryptionData = async () => {
    await chrome.storage.local.remove(STORAGE_KEY);
  };

  const updateLastActivity = async () => {
    const stored = await getStoredEncryptionData();
    if (stored) {
      stored.lastActivity = Date.now();
      await chrome.storage.local.set({ [STORAGE_KEY]: stored });
    }
  };

  // 移除所有 background 通信，改为依赖存储变化

  // ========== 用户权限和限制检查 ==========
  const checkUserAccess = () => {
    const { user: currentUser } = useUserSession();
    if (!currentUser.value) {
      throw new Error('Please sign in to use encrypted bookmarks');
    }
    return currentUser.value;
  };

  const checkUserBookmarkLimit = async () => {
    const user = checkUserAccess();

    // 获取用户计划信息（从 bookmark-web 或其他方式）
    // 暂时跳过计划检查，因为计划信息不再在 session 中
    // TODO: 如果需要计划限制，可以通过单独的 API 获取用户计划信息

    // Free 用户检查数量限制（暂时对所有用户应用限制）
    const count = await rxdb.extras.count({
      selector: {
        bookmarkType: 'encrypted',
        userId: user.id
      }
    }).exec();

    if (count >= FREE_USER_BOOKMARK_LIMIT) {
      throw new Error('NEED_UPGRADE'); // 特殊错误码，用于触发升级对话框
    }
  };

  // ========== 核心加密功能 ==========
  const hasStoredEncryption = async () => {
    const { user: currentUser } = useUserSession();
    if (!currentUser.value) {
      console.log('hasStoredEncryption: no user');
      return false;
    }

    const stored = await getStoredEncryptionData();
    if (!stored) {
      console.log('hasStoredEncryption: no stored data');
      return false;
    }

    const isValid = (Date.now() - stored.lastActivity <= LOCK_TIMEOUT);
    console.log('hasStoredEncryption:', {
      stored: !!stored,
      lastActivity: stored?.lastActivity,
      now: Date.now(),
      timeDiff: Date.now() - stored.lastActivity,
      lockTimeout: LOCK_TIMEOUT,
      isValid
    });

    // 如果数据过期，主动清除
    if (!isValid) {
      console.log('hasStoredEncryption: data expired, clearing storage');
      await clearStoredEncryptionData();
      return false;
    }

    return true;
  };

  /**
   * 从 bookmark-web 获取加密密钥配置
   */
  const getCryptoKeysFromWeb = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/account/encrypt-key`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Failed to fetch crypto keys from web:', error);
      throw error;
    }
  };

  const unlock = async (password: string) => {
    const { user: currentUser } = useUserSession();

    try {
      // 先检查是否有有效的存储数据
      const stored = await getStoredEncryptionData();
      const isValid = stored && (Date.now() - stored.lastActivity <= LOCK_TIMEOUT);

      if (stored && isValid) {
        // 从存储恢复
        const keyBuffer = base64ToArrayBuffer(stored.userDataKey);
        userDataKey.value = await crypto.subtle.importKey(
          "raw", keyBuffer,
          { name: "AES-GCM", length: 128 },
          true, ["encrypt", "decrypt"]
        );
        masterIV.value = new Uint8Array(base64ToArrayBuffer(stored.masterIV));
      } else {
        // 如果有过期数据，清除它
        if (stored && !isValid) {
          console.log('unlock: clearing expired storage data');
          await clearStoredEncryptionData();
        }

        console.log('unlock: generating new key with password');

        // 尝试从 bookmark-web 获取加密配置
        let cryptoKeys = null;
        if (currentUser.value) {
          try {
            cryptoKeys = await getCryptoKeysFromWeb();
          } catch (error) {
            console.warn('Failed to fetch crypto keys from web, falling back to user session:', error);
          }
        }

        // 生成或恢复密钥
        if (cryptoKeys?.hasEncryptionSetup) {
          // 从 bookmark-web 获取的配置
          const { encryptSalt, encryptKey, encryptIv } = cryptoKeys;
          userDataKey.value = await restoreDataKey(password, encryptSalt, encryptKey, encryptIv);
          masterIV.value = generateIv();
        } else {
          // 匿名用户或未配置加密的用户创建新密钥
          const { dataKey } = await generateEncryptKey(password);
          userDataKey.value = dataKey;
          masterIV.value = generateIv();
        }

        // 存储到 Extension Storage
        await storeEncryptionData(userDataKey.value, masterIV.value);
      }

      // 存储数据变化会自动通知 background script
      toast.success("Encryption unlocked successfully");
    } catch (error) {
      toast.error("Failed to unlock encryption");
      throw error;
    }
  };

  const lock = async () => {
    userDataKey.value = null;
    masterIV.value = null;
    await clearStoredEncryptionData();
    // 存储数据变化会自动通知 background script
    toast.info("Encryption locked");
  };

  const resetActivity = async () => {
    if (!isLocked.value) {
      await updateLastActivity();
      // 存储数据变化会自动通知 background script
    }
  };

  const setUserEncryptKey = async (password: string) => {
    const { user: currentUser } = useUserSession();
    if (!currentUser.value) {
      return;
    }
    // 检查是否已经设置过加密密钥
    try {
      const existingKeys = await getCryptoKeysFromWeb();
      if (existingKeys?.hasEncryptionSetup) {
        toast.error("Encryption is already configured for this user.");
        return;
      }
    } catch (error) {
      // 如果获取失败，继续设置流程
      console.warn('Failed to check existing encryption setup:', error);
    }

    const { dataKey, encryptSalt, encryptKey, encryptIv, encryptAlgo } = await generateEncryptKey(password);

    try {
      await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/account/encrypt-key`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          encryptSalt,
          encryptKey,
          encryptIv,
          encryptAlgo
        }),
      });
      userDataKey.value = dataKey;
      masterIV.value = generateIv();
      await storeEncryptionData(userDataKey.value, masterIV.value);
      toast.success("Encryption configured successfully");
    } catch (error) {
      toast.error("Failed to configure encryption");
    }
  };

  // 重置主密码
  const resetUserEncryptKey = async (newPassword: string) => {
    const { user: currentUser } = useUserSession();
    if (!currentUser.value) {
      return;
    }
    if (!userDataKey.value) {
      toast.error("Please unlock the encryption system first");
      return;
    }

    try {
      // 获取当前的加密配置
      const currentKeys = await getCryptoKeysFromWeb();
      if (!currentKeys?.hasEncryptionSetup || !currentKeys.encryptSalt) {
        toast.error("Encryption configuration not found");
        return;
      }

      const { encryptKey, encryptIv } = await resetEncryptKey(
        newPassword,
        userDataKey.value,
        currentKeys.encryptSalt
      );


      // 调用 API 更新数据库，仅更新 encryptKey 和 encryptIv, 其他字段保持不变
      await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/account/encrypt-key`, {
        method: "POST",
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          encryptSalt: currentKeys.encryptSalt,
          encryptKey,
          encryptIv,
          encryptAlgo: currentKeys.encryptAlgo || 'AES-GCM-128'
        }),
      });

      toast.success("Password changed successfully!");
    } catch (error) {
      toast.error("Failed to change password");
    }
  };



  // ========== 书签加密/解密 ==========
  const encryptBookmark = async (bookmark: any) => {
    if (isLocked.value) {
      toast.error("Please unlock the encryption system first");
      return null;
    }

    try {
      const result = { ...bookmark };
      const iv = arrayBufferToBase64(masterIV.value!.buffer as ArrayBuffer);

      const { ciphertext: encryptedTitle } = await encryptGCM(
        bookmark.title, iv, userDataKey.value!
      );
      result.title = encryptedTitle;
      result.iv = iv;

      if (bookmark.url) {
        const { ciphertext: encryptedUrl } = await encryptGCM(
          bookmark.url, iv, userDataKey.value!
        );
        // 使用自定义协议 encrypted:// 包装加密字符串
        const base64Encoded = btoa(encryptedUrl).replace(/[+/=]/g, (match) => {
          return { '+': '-', '/': '_', '=': '' }[match] || match;
        });
        result.url = `encrypted://${base64Encoded}`;
      }

      await resetActivity();
      return result;
    } catch (error) {
      toast.error("Failed to encrypt bookmark");
      return null;
    }
  };

  const decryptBookmark = async (bookmark: any) => {
    if (isLocked.value) return null;

    try {
      const result = { ...bookmark };

      result.title = await decryptGCM(
        bookmark.title, bookmark.iv, userDataKey.value!
      );

      if (bookmark.url) {
        // 检查是否是加密的 URL 格式
        if (bookmark.url.startsWith('encrypted://')) {
          // 从 URL 中提取 Base64 编码的加密字符串
          const base64Encoded = bookmark.url.replace('encrypted://', '');
          // 恢复 Base64 格式
          const base64Restored = base64Encoded.replace(/-/g, '+').replace(/_/g, '/');
          // 添加必要的填充
          const padding = '='.repeat((4 - base64Restored.length % 4) % 4);
          const encryptedData = atob(base64Restored + padding);

          result.url = await decryptGCM(
            encryptedData, bookmark.iv, userDataKey.value!
          );
        } else {
          // 兼容旧格式：直接解密
          result.url = await decryptGCM(
            bookmark.url, bookmark.iv, userDataKey.value!
          );
        }
      }

      await resetActivity();
      return result;
    } catch (error) {
      toast.error("Failed to decrypt bookmark");
      return null;
    }
  };

  // 保留原有的字符串加密功能
  const encryptString = async (data: string): Promise<any | null> => {
    if (!userDataKey.value) {
      toast.error("Please unlock the encryption system first");
      return null;
    }
    try {
      const iv = arrayBufferToBase64(generateIv().buffer);
      const { ciphertext } = await encryptGCM(
        data,
        iv,
        userDataKey.value
      );
      return { ciphertext, iv };
    } catch (error) {
      toast.error("Failed to encrypt");
      return null;
    }
  };

  const decryptString = async (encryptedData: string, iv: string): Promise<string | null> => {
    if (!userDataKey.value) {
      toast.error("Please unlock the encryption system first");
      return null;
    }
    try {
      const decrypted = await decryptGCM(encryptedData, iv, userDataKey.value);
      return decrypted;
    } catch (error) {
      toast.error("Failed to decrypt");
      return null;
    }
  };

  // ========== 加密书签 CRUD 操作 ==========
  const createEncryptedBookmark = async (bookmark: any, parentId: string) => {
    checkUserAccess(); // 确保用户已登录
    await checkUserBookmarkLimit(); // 检查书签数量限制

    const encryptedData = await encryptBookmark(bookmark);
    if (!encryptedData) return null;

    try {
      // 在指定的父文件夹中创建 Chrome 书签
      const chromeBookmark = await chrome.bookmarks.create({
        parentId: parentId,
        title: encryptedData.title,
        url: encryptedData.url
      });

      // 创建 RxDB 记录
      const user = checkUserAccess(); // 再次确认用户已登录
      await rxdb.extras.insert({
        bookmarkId: chromeBookmark.id,
        userId: user.id,
        bookmarkType: 'encrypted',
        iv: encryptedData.iv,
        note: '',
        emoji: ''
      });

      toast.success("Encrypted bookmark created successfully");
      return chromeBookmark;
    } catch (error) {
      toast.error("Failed to create encrypted bookmark");
      return null;
    }
  };

  const getEncryptedBookmarks = async () => {
    const user = checkUserAccess(); // 确保用户已登录

    const extras = await rxdb.extras.find({
      selector: {
        bookmarkType: 'encrypted',
        userId: user.id
      }
    }).exec();

    const bookmarks = [];
    for (const extra of extras) {
      try {
        const chromeBookmarks = await chrome.bookmarks.get(extra.bookmarkId);
        bookmarks.push({
          ...chromeBookmarks[0],
          iv: extra.iv,
          extra: extra
        });
      } catch (error) {
        // Chrome 书签已被删除，清理 extra 记录
        await extra.remove();
      }
    }

    return bookmarks;
  };

  return {
    isLocked,
    hasStoredEncryption,
    unlock,
    lock,
    resetActivity,
    encryptBookmark,
    decryptBookmark,
    createEncryptedBookmark,
    getEncryptedBookmarks,
    checkUserAccess,
    checkUserBookmarkLimit,
    // 保留现有的方法
    setUserEncryptKey,
    resetUserEncryptKey,
    encryptString,
    decryptString
  };
}
